from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
import uuid

# Using cached chat service instead of create_chat_service
from core.security import get_tenant_info
from core.cache_manager import get_cache_manager
from models.user import UserTenantDB
from utils import setup_colored_logging, log_info, log_error, log_success
import logging

# Setup logging
setup_colored_logging()
logger = logging.getLogger(__name__)

router = APIRouter(tags=["Chat"])


class ChatRequest(BaseModel):
    message: str


class ToolUsed(BaseModel):
    name: str
    description: str
    input: dict = {}
    output: str = ""

class ChatResponse(BaseModel):
    response: str
    thread_id: str
    user_id: str
    tools_used: list[ToolUsed] = []
    message_id: str  # Unique ID for this chat exchange


@router.post("/chat", response_model=ChatResponse)
async def chat(
    chat_request: ChatRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Chat endpoint that uses ChatService with current user context
    The user ID from the token is used as the thread ID for conversation continuity
    """
    try:
        # Use cached chat service for better performance
        cache_manager = get_cache_manager()
        chat_service = cache_manager.get_or_create_chat_service(current_user)

        # Use user ID as thread ID for conversation continuity per user
        thread_id = str(current_user.user.id)
        user_id = str(current_user.user.id)

        logger.info(f"Chat request from user {current_user.user.username} (ID: {user_id}, tenant: {current_user.tenant_id}): {chat_request.message[:50]}...")

        # Get response from chat service (automatically uses current user's vector store and agents)
        agent_response = chat_service.chat(chat_request.message, thread_id)

        logger.info(f"✅ Chat response generated for user {current_user.user.username}")

        # Handle both old string format and new dict format for backward compatibility
        logger.debug("Processing agent response...")
        if isinstance(agent_response, dict):
            response_text = agent_response.get("response", "")
            logger.debug(f"Response text extracted: {len(response_text)} chars")

            # Safely process tools_used to prevent hanging
            tools_used = []
            try:
                raw_tools = agent_response.get("tools_used", [])
                logger.debug(f"Processing {len(raw_tools)} tools...")
                for i, tool in enumerate(raw_tools):
                    logger.debug(f"Processing tool {i}: {tool.get('name', 'unknown')}")
                    tools_used.append(ToolUsed(**tool))
                logger.debug("Tools processed successfully")
            except Exception as e:
                logger.warning(f"Tool processing failed: {e}")
                tools_used = []
        else:
            response_text = agent_response
            tools_used = []

        logger.debug("Creating ChatResponse...")
        chat_response = ChatResponse(
            response=response_text,
            thread_id=thread_id,
            user_id=user_id,
            message_id=str(uuid.uuid4()),
            tools_used=tools_used
        )
        logger.debug("ChatResponse created, returning...")
        return chat_response
        
    except Exception as e:
        log_error(f"Chat failed for user {current_user.user.username}", e)
        raise HTTPException(
            status_code=500,
            detail="Failed to process chat request"
        )


# Streaming endpoint removed - using normal chat only


@router.delete("/chat/clear")
async def clear_conversation(
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Completely clear ALL user data including conversation history, memory, profile, and sessions
    This is a comprehensive reset that removes all traces of the user's interactions
    """
    try:
        log_info(f"Clearing ALL data for user {current_user.user.username}")

        # Get production memory manager for comprehensive deletion
        from utils.production_memory_manager import get_production_memory_manager
        memory_manager = get_production_memory_manager(current_user.tenant_id)

        user_id = str(current_user.user.id)

        # Use comprehensive deletion function
        results = memory_manager.clear_all_user_data(user_id)

        if results["success"]:
            total_deleted = (results["chat_history_deleted"] + results["memories_deleted"] +
                           results["sessions_deleted"] + results["checkpoints_deleted"] +
                           results["checkpoint_writes_deleted"])

            log_success(f"Completely cleared all data for user {current_user.user.username}: {total_deleted} total items deleted")

            message = f"All data cleared for user {current_user.user.username}"
            if total_deleted > 0:
                message += f" ({total_deleted} items deleted)"

            return {
                "status": "success",
                "message": message,
                "user_id": user_id,
                "deletion_summary": {
                    "chat_messages": results["chat_history_deleted"],
                    "memories": results["memories_deleted"],
                    "profile_deleted": results["profile_deleted"],
                    "sessions": results["sessions_deleted"],
                    "checkpoints": results["checkpoints_deleted"],
                    "checkpoint_writes": results["checkpoint_writes_deleted"],
                    "total_deleted": total_deleted
                },
                "errors": results["errors"] if results["errors"] else None
            }
        else:
            log_error(f"Failed to completely clear data for user {current_user.user.username}: {results['errors']}")
            return {
                "status": "partial_success",
                "message": f"Partially cleared data for user {current_user.user.username}",
                "user_id": user_id,
                "deletion_summary": {
                    "chat_messages": results["chat_history_deleted"],
                    "memories": results["memories_deleted"],
                    "profile_deleted": results["profile_deleted"],
                    "sessions": results["sessions_deleted"],
                    "checkpoints": results["checkpoints_deleted"],
                    "checkpoint_writes": results["checkpoint_writes_deleted"]
                },
                "errors": results["errors"]
            }

    except Exception as e:
        log_error(f"Clear all data failed for user {current_user.user.username}", e)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear all user data: {str(e)}"
        )


@router.get("/chat/health")
async def chat_health_check():
    """
    Health check endpoint for chat service
    """
    return {
        "status": "healthy",
        "service": "chat",
        "message": "Chat service is running with ChatService architecture"
    }
