"""
Enhanced Tool Manager - Supports parallel tool execution and Nepali language handling
Optimized for better performance and multilingual support
"""

import logging
import time
import asyncio
import concurrent.futures
from typing import List, Dict, Any, Tuple, Optional
from langchain_core.tools import tool, Tool
from langchain_google_genai import ChatGoogleGenerativeAI
from models.user import UserTenantDB
from models.tool_usage import ToolUsageCreate
from api.services.tool_usage_service import ToolUsageService
from utils import log_tool_call, log_tool_result
import os
from dotenv import load_dotenv

load_dotenv()
logger = logging.getLogger(__name__)


class LanguageProcessor:
    """Handles Nepali language detection and processing"""
    
    def __init__(self):
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            temperature=0.1,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )
        
        # Common Nepali words/phrases for quick detection
        self.nepali_indicators = [
            'नमस्ते', 'धन्यवाद', 'कस्तो', 'छ', 'हो', 'छैन', 'गर्नुहोस्',
            'मलाई', 'तपाईं', 'हामी', 'उनी', 'यो', 'त्यो', 'के', 'कहाँ',
            'कहिले', 'किन', 'कसरी', 'कति', 'राम्रो', 'नराम्रो'
        ]
    
    def detect_nepali(self, text: str) -> bool:
        """Quick detection of Nepali text"""
        # Check for Devanagari script
        for char in text:
            if '\u0900' <= char <= '\u097F':  # Devanagari Unicode range
                return True
        
        # Check for common Nepali words in romanized form
        text_lower = text.lower()
        nepali_romanized = [
            'namaste', 'dhanyabad', 'kasto', 'ramro', 'naramro',
            'malai', 'tapai', 'hami', 'uni', 'yo', 'tyo'
        ]
        
        for word in nepali_romanized:
            if word in text_lower:
                return True
                
        return False
    
    async def translate_to_english(self, nepali_text: str) -> str:
        """Translate Nepali text to English for processing"""
        try:
            prompt = f"""
            Translate the following Nepali text to English. If it's already in English or mixed, just return the English version:
            
            Text: {nepali_text}
            
            Provide only the English translation, no explanations.
            """
            
            response = await asyncio.to_thread(
                self.llm.invoke,
                [{"role": "user", "content": prompt}]
            )
            
            return response.content.strip()
        except Exception as e:
            logger.warning(f"Translation failed: {e}")
            return nepali_text  # Return original if translation fails


class ParallelToolExecutor:
    """Handles parallel execution of multiple tools"""
    
    def __init__(self, max_workers: int = 3):
        self.max_workers = max_workers
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
    
    async def execute_parallel(self, tool_calls: List[Tuple[callable, Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """Execute multiple tools in parallel"""
        if not tool_calls:
            return []
        
        # Create tasks for parallel execution
        tasks = []
        for tool_func, kwargs in tool_calls:
            task = asyncio.create_task(
                asyncio.to_thread(tool_func, **kwargs)
            )
            tasks.append(task)
        
        # Execute all tasks concurrently
        results = []
        try:
            completed_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for i, result in enumerate(completed_results):
                tool_name = tool_calls[i][0].__name__ if hasattr(tool_calls[i][0], '__name__') else 'unknown'
                
                if isinstance(result, Exception):
                    results.append({
                        'tool': tool_name,
                        'success': False,
                        'result': f"Error: {str(result)}",
                        'error': str(result)
                    })
                else:
                    results.append({
                        'tool': tool_name,
                        'success': True,
                        'result': result,
                        'error': None
                    })
                    
        except Exception as e:
            logger.error(f"Parallel execution failed: {e}")
            # Return error results for all tools
            for tool_func, _ in tool_calls:
                tool_name = tool_func.__name__ if hasattr(tool_func, '__name__') else 'unknown'
                results.append({
                    'tool': tool_name,
                    'success': False,
                    'result': f"Execution error: {str(e)}",
                    'error': str(e)
                })
        
        return results
    
    def __del__(self):
        """Cleanup executor on deletion"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)


class EnhancedToolManager:
    """Enhanced tool manager with parallel execution and Nepali support"""
    
    def __init__(self, current_user: UserTenantDB, tool_config=None):
        self.current_user = current_user
        self.tenant_id = current_user.tenant_id if current_user else None
        self.vector_store_manager = current_user.vector_store_manager if current_user else None
        self.tool_config = tool_config or self._get_default_config()
        
        # Initialize language processor and parallel executor
        self.language_processor = LanguageProcessor()
        self.parallel_executor = ParallelToolExecutor()
        
        # Tool usage tracking
        self.tool_usage_service = ToolUsageService() if self.tenant_id else None
        
        logger.info(f"✅ Enhanced Tool Manager initialized for tenant: {self.tenant_id}")
    
    def _get_default_config(self):
        """Get default tool configuration"""
        return {
            'search_information': True,
            'search_products': True,
            'handle_booking': True,
            'parallel_execution': True,
            'nepali_support': True
        }
    
    async def process_message_with_language_support(self, user_message: str) -> Tuple[str, bool]:
        """Process message with Nepali language support"""
        is_nepali = self.language_processor.detect_nepali(user_message)
        
        if is_nepali and self.tool_config.get('nepali_support', True):
            # Translate to English for tool processing
            english_message = await self.language_processor.translate_to_english(user_message)
            logger.info(f"🌐 Translated Nepali message to English for processing")
            return english_message, is_nepali
        
        return user_message, is_nepali
    
    def create_enhanced_tools(self) -> List[Tool]:
        """Create enhanced tools with parallel execution support"""
        tools = []
        
        if self.tool_config.get('search_information', True):
            tools.append(self._create_enhanced_search_information_tool())
            
        if self.tool_config.get('search_products', True):
            tools.append(self._create_enhanced_search_products_tool())
            
        if self.tool_config.get('handle_booking', True):
            tools.append(self._create_enhanced_booking_tool())
        
        # Add intelligent search tool that can use multiple tools in parallel
        if self.tool_config.get('parallel_execution', True):
            tools.append(self._create_intelligent_search_tool())
        
        logger.info(f"🛠️ Created {len(tools)} enhanced tools")
        return tools
    
    def _create_enhanced_search_information_tool(self):
        """Create enhanced search information tool"""
        
        @tool
        async def search_information_enhanced(user_message: str) -> str:
            """
            Enhanced search for information with Nepali language support.
            Handles general queries, troubleshooting, and help requests.
            
            Args:
                user_message: User's message (supports Nepali and English)
                
            Returns:
                Search results for information queries
            """
            log_tool_call("search_information_enhanced", f"user_message='{user_message}'")
            start_time = time.time()
            
            try:
                # Process language
                processed_message, is_nepali = await self.process_message_with_language_support(user_message)
                
                # Search for information
                result = self.vector_store_manager.search_information(processed_message)
                
                # Add language indicator to result if needed
                if is_nepali:
                    result = f"[Nepali Query Processed] {result}"
                
                execution_time = (time.time() - start_time) * 1000
                
                # Log usage
                await self._log_tool_usage_async(
                    "search_information_enhanced",
                    "Enhanced information search with language support",
                    {"user_message": user_message, "processed_message": processed_message},
                    result,
                    execution_time,
                    True
                )
                
                log_tool_result(result)
                return result
                
            except Exception as e:
                execution_time = (time.time() - start_time) * 1000
                error_msg = f"Error in enhanced information search: {str(e)}"
                
                await self._log_tool_usage_async(
                    "search_information_enhanced",
                    "Enhanced information search with language support",
                    {"user_message": user_message},
                    error_msg,
                    execution_time,
                    False,
                    str(e)
                )
                
                log_tool_result(error_msg)
                return error_msg
        
        return search_information_enhanced
