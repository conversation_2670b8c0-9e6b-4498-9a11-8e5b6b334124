"""
Search Agent V2 - Clean and simple search agent for ChatService
"""

import logging

logger = logging.getLogger(__name__)


class SearchAgentV2:
    """Clean search agent that works with vector store manager"""

    def __init__(self, vector_store_manager):
        """Initialize with vector store manager"""
        self.vector_manager = vector_store_manager
        logger.info("✅ Search Agent V2 initialized")

    def search_information(self, query: str) -> str:
        """Search for information using vector store"""
        try:
            logger.info(f"🔍 Information search: {query}")
            result = self.vector_manager.search_information(query)
            logger.info(f"✅ Information search completed")
            return result
        except Exception as e:
            logger.error(f"Information search failed: {e}")
            return f"Error searching for information: {str(e)}"

    def search_products(self, query: str) -> str:
        """Search for products using vector store"""
        try:
            logger.info(f"🛍️ Product search: {query}")
            result = self.vector_manager.search_products(query)
            logger.info(f"✅ Product search completed")
            return result
        except Exception as e:
            logger.error(f"Product search failed: {e}")
            return f"Error searching for products: {str(e)}"