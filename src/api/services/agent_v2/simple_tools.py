"""
Simple Tools - No hardcoding, delegate to existing systems
"""

import logging
from langchain_core.tools import tool
from models.user import UserTenantDB

logger = logging.getLogger(__name__)


def create_simple_tools(current_user: UserTenantDB):
    """Create simple tools that delegate to existing systems"""
    
    @tool
    def search_information(query: str) -> str:
        """Search for general information, help, and answers to questions"""
        try:
            if current_user and current_user.vector_store_manager:
                result = current_user.vector_store_manager.search_information(query)
                logger.info(f"✅ Information search completed for: {query}")
                return result
            else:
                return "I can help you with information. What would you like to know?"
        except Exception as e:
            logger.error(f"Information search failed: {e}")
            return "I'm here to help with any questions you have."

    @tool  
    def search_products(query: str) -> str:
        """Search for courses, programs, and educational products"""
        try:
            if current_user and current_user.vector_store_manager:
                result = current_user.vector_store_manager.search_products(query)
                logger.info(f"✅ Product search completed for: {query}")
                return result
            else:
                return "We offer various educational courses. What specific course interests you?"
        except Exception as e:
            logger.error(f"Product search failed: {e}")
            return "We have many programs available. Let me know what you're interested in."

    @tool
    def handle_booking(user_message: str) -> str:
        """Handle course booking and enrollment requests"""
        try:
            logger.info(f"📅 Booking request: {user_message}")

            # Just return a simple response - let the agent handle all logic
            return "I'll help you with booking. Let me assist you with the enrollment process."

        except Exception as e:
            logger.error(f"Booking handling failed: {e}")
            return "I'll help you with booking."

    return [search_information, search_products, handle_booking]
