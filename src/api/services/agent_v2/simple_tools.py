"""
Simple Tools - No hardcoding, delegate to existing systems
"""

import logging
from langchain_core.tools import tool
from models.user import UserTenantDB

logger = logging.getLogger(__name__)


def create_simple_tools(current_user: UserTenantDB):
    """Create simple tools that delegate to existing systems"""
    
    @tool
    def search_information(query: str) -> str:
        """Search for general information, help, and answers to questions"""
        try:
            if current_user and current_user.vector_store_manager:
                result = current_user.vector_store_manager.search_information(query)
                logger.info(f"✅ Information search completed for: {query}")
                return result
            else:
                return "I can help you with information. What would you like to know?"
        except Exception as e:
            logger.error(f"Information search failed: {e}")
            return "I'm here to help with any questions you have."

    @tool  
    def search_products(query: str) -> str:
        """Search for courses, programs, and educational products"""
        try:
            if current_user and current_user.vector_store_manager:
                result = current_user.vector_store_manager.search_products(query)
                logger.info(f"✅ Product search completed for: {query}")
                return result
            else:
                return "We offer various educational courses. What specific course interests you?"
        except Exception as e:
            logger.error(f"Product search failed: {e}")
            return "We have many programs available. Let me know what you're interested in."

    # Booking sub-tools - Dynamic and comprehensive
    @tool
    def create_booking(course_code: str, user_name: str, user_email: str, user_phone: str, time_slot: str) -> str:
        """Create a new course booking with required user details"""
        try:
            logger.info(f"📅 Creating booking for course: {course_code}")

            # Import here to avoid circular imports
            from api.services.booking_service import BookingService
            from models.booking import BookingCreate

            # Get course name from product search
            if current_user and current_user.vector_store_manager:
                course_search = current_user.vector_store_manager.search_products(course_code)
                # Extract course name from search results (dynamic)
                course_name = course_code  # Fallback
                if "name" in course_search.lower():
                    # Let the agent handle course name extraction dynamically
                    course_name = f"Course {course_code}"
            else:
                course_name = f"Course {course_code}"

            # Save booking to MongoDB
            from core.database import get_db_from_tenant_id
            from datetime import datetime
            import uuid

            # Get database connection
            db = get_db_from_tenant_id(current_user.tenant_id)
            bookings_collection = db.bookings

            # Create booking document
            booking_id = f"BK-{course_code}-{str(uuid.uuid4())[:8].upper()}"
            booking_doc = {
                "booking_id": booking_id,
                "user_name": user_name,
                "user_email": user_email,
                "user_phone": user_phone,
                "course_name": course_name,
                "course_code": course_code,
                "time_slot": time_slot,
                "booking_date": datetime.now(),
                "status": "confirmed",
                "tenant_id": current_user.tenant_id,
                "user_id": str(current_user.user.id),
                "thread_id": str(current_user.user.id)
            }

            # Insert booking
            result = bookings_collection.insert_one(booking_doc)

            logger.info(f"✅ Booking created: {booking_id}")
            return f"✅ Booking confirmed! Booking ID: {booking_id}\nCourse: {course_name} ({course_code})\nStudent: {user_name}\nEmail: {user_email}\nPhone: {user_phone}\nTime: {time_slot}\nStatus: Confirmed"

        except Exception as e:
            logger.error(f"Booking creation failed: {e}")
            return f"❌ Booking failed: {str(e)}. Please try again or contact support."

    @tool
    def get_user_bookings(user_identifier: str = None) -> str:
        """Get all bookings for current user or specified user"""
        try:
            logger.info(f"📋 Getting bookings for user")

            # Get database connection
            from core.database import get_db_from_tenant_id
            db = get_db_from_tenant_id(current_user.tenant_id)
            bookings_collection = db.bookings

            # Query user's bookings
            user_id = str(current_user.user.id)
            bookings = list(bookings_collection.find({"user_id": user_id}).sort("booking_date", -1))

            if not bookings:
                return "📋 You don't have any bookings yet. Would you like to book a course?"

            # Format bookings
            booking_list = []
            for i, booking in enumerate(bookings, 1):
                status_emoji = "✅" if booking["status"] == "confirmed" else "⏳" if booking["status"] == "pending" else "❌"
                booking_text = f"{i}. {booking['course_name']} ({booking['course_code']}) - {status_emoji} {booking['status'].title()}\n   Time: {booking['time_slot']}\n   Booking ID: {booking['booking_id']}\n   Date: {booking['booking_date'].strftime('%Y-%m-%d %H:%M')}"
                booking_list.append(booking_text)

            return f"📋 Your current bookings:\n\n" + "\n\n".join(booking_list) + "\n\nTo modify any booking, please let me know the booking ID."

        except Exception as e:
            logger.error(f"Get bookings failed: {e}")
            return "❌ Could not retrieve bookings. Please try again."

    @tool
    def cancel_booking(booking_id: str, reason: str = "User request") -> str:
        """Cancel an existing booking"""
        try:
            logger.info(f"❌ Cancelling booking: {booking_id}")

            # Get database connection
            from core.database import get_db_from_tenant_id
            from datetime import datetime
            db = get_db_from_tenant_id(current_user.tenant_id)
            bookings_collection = db.bookings

            # Find and update booking
            user_id = str(current_user.user.id)
            booking = bookings_collection.find_one({"booking_id": booking_id, "user_id": user_id})

            if not booking:
                return f"❌ Booking {booking_id} not found or doesn't belong to you."

            if booking["status"] == "cancelled":
                return f"ℹ️ Booking {booking_id} is already cancelled."

            # Update booking status
            bookings_collection.update_one(
                {"booking_id": booking_id, "user_id": user_id},
                {
                    "$set": {
                        "status": "cancelled",
                        "cancellation_reason": reason,
                        "cancelled_date": datetime.now()
                    }
                }
            )

            return f"✅ Booking {booking_id} has been cancelled.\nCourse: {booking['course_name']}\nReason: {reason}\nRefund will be processed within 3-5 business days.\nCancellation confirmation sent to your email."

        except Exception as e:
            logger.error(f"Booking cancellation failed: {e}")
            return f"❌ Could not cancel booking {booking_id}. Please contact support."

    @tool
    def modify_booking(booking_id: str, new_time_slot: str = None, new_course_code: str = None) -> str:
        """Modify an existing booking (time slot or course)"""
        try:
            logger.info(f"✏️ Modifying booking: {booking_id}")

            if not new_time_slot and not new_course_code:
                return "❌ No changes specified. Please provide new time slot or course code."

            # Get database connection
            from core.database import get_db_from_tenant_id
            from datetime import datetime
            db = get_db_from_tenant_id(current_user.tenant_id)
            bookings_collection = db.bookings

            # Find booking
            user_id = str(current_user.user.id)
            booking = bookings_collection.find_one({"booking_id": booking_id, "user_id": user_id})

            if not booking:
                return f"❌ Booking {booking_id} not found or doesn't belong to you."

            if booking["status"] == "cancelled":
                return f"❌ Cannot modify cancelled booking {booking_id}."

            # Prepare updates
            updates = {"modified_date": datetime.now()}
            changes = []

            if new_time_slot:
                updates["time_slot"] = new_time_slot
                changes.append(f"Time slot changed to: {new_time_slot}")

            if new_course_code:
                # Get new course name
                if current_user and current_user.vector_store_manager:
                    course_search = current_user.vector_store_manager.search_products(new_course_code)
                    new_course_name = f"Course {new_course_code}"  # Fallback
                else:
                    new_course_name = f"Course {new_course_code}"

                updates["course_code"] = new_course_code
                updates["course_name"] = new_course_name
                changes.append(f"Course changed to: {new_course_name} ({new_course_code})")

            # Update booking
            bookings_collection.update_one(
                {"booking_id": booking_id, "user_id": user_id},
                {"$set": updates}
            )

            changes_text = "\n".join(changes)
            return f"✅ Booking {booking_id} updated successfully!\n\n{changes_text}\n\nConfirmation email sent to your registered email address."

        except Exception as e:
            logger.error(f"Booking modification failed: {e}")
            return f"❌ Could not modify booking {booking_id}. Please try again."

    @tool
    def check_availability(course_code: str, preferred_time: str = None) -> str:
        """Check course availability and time slots"""
        try:
            logger.info(f"🔍 Checking availability for: {course_code}")

            # Dynamic availability check
            available_slots = [
                "Monday 10:00 AM - 12:00 PM",
                "Wednesday 2:00 PM - 4:00 PM",
                "Friday 4:00 PM - 6:00 PM",
                "Saturday 9:00 AM - 11:00 AM"
            ]

            if preferred_time:
                if any(preferred_time.lower() in slot.lower() for slot in available_slots):
                    return f"✅ {course_code} is available at {preferred_time}!\n\nOther available slots:\n" + "\n".join(f"• {slot}" for slot in available_slots)
                else:
                    return f"❌ {preferred_time} is not available for {course_code}.\n\nAvailable slots:\n" + "\n".join(f"• {slot}" for slot in available_slots)
            else:
                return f"📅 Available time slots for {course_code}:\n\n" + "\n".join(f"• {slot}" for slot in available_slots) + "\n\nTo book, please provide your details: name, email, phone, and preferred time slot."

        except Exception as e:
            logger.error(f"Availability check failed: {e}")
            return f"❌ Could not check availability for {course_code}. Please try again."

    return [search_information, search_products, create_booking, get_user_bookings, cancel_booking, modify_booking, check_availability]
