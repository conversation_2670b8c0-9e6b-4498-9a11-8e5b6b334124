"""
New Chat Service - Simple implementation with new agent
"""

import logging
from typing import Optional
from models.user import UserTenantDB
from api.services.agent_v2.new_simple_agent import NewSimpleAgent
from api.services.agent_v2.simple_tools import create_simple_tools

logger = logging.getLogger(__name__)


class NewChatService:
    """
    New Chat Service - Simple and efficient
    """

    def __init__(self, current_user: UserTenantDB):
        """Initialize chat service"""
        self.current_user = current_user
        self.tenant_id = current_user.tenant_id
        
        # Initialize agent
        self._agent: Optional[NewSimpleAgent] = None
        
        logger.info(f"NewChatService initialized for tenant: {self.tenant_id}")

    @property
    def agent(self) -> NewSimpleAgent:
        """Get or initialize agent"""
        if self._agent is None:
            logger.debug("Initializing New Simple Agent...")
            self._agent = NewSimpleAgent(current_user=self.current_user)
            
            # Create and set tools
            tools = create_simple_tools(self.current_user)
            self._agent.set_tools(tools)
            
            logger.info("✅ New Simple Agent initialized with tools")
        return self._agent

    def chat(self, message: str, thread_id: str = None) -> dict:
        """Process chat message"""
        if thread_id is None:
            thread_id = str(self.current_user.user.id)

        logger.debug(f"Processing chat for user {self.current_user.user.username}")
        
        return self.agent.chat(message, thread_id)

    async def chat_stream(self, message: str, thread_id: str = None):
        """Stream chat response"""
        if thread_id is None:
            thread_id = str(self.current_user.user.id)

        logger.debug(f"Streaming chat for user {self.current_user.user.username}")
        
        async for chunk in self.agent.chat_stream(message, thread_id):
            yield chunk
